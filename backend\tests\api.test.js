const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../src/server');
const NATORank = require('../src/models/NATORank');
const Rank = require('../src/models/Rank');

/**
 * API Test Suite
 * Sprint Backend: API de Rangos
 * 
 * Integration tests for NATO Rank and Rank API endpoints
 * Tests all CRUD operations and edge cases
 */

// Test database connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/personnel_management_test';

beforeAll(async () => {
  await mongoose.connect(MONGODB_URI);
});

afterAll(async () => {
  await mongoose.connection.close();
});

beforeEach(async () => {
  // Clean database before each test
  await NATORank.deleteMany({});
  await Rank.deleteMany({});
});

describe('NATO Ranks API', () => {
  describe('GET /api/v1/nato-ranks', () => {
    test('should get empty list when no NATO ranks exist', async () => {
      const res = await request(app)
        .get('/api/v1/nato-ranks')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toEqual([]);
      expect(res.body.pagination.totalItems).toBe(0);
    });

    test('should get paginated NATO ranks', async () => {
      // Create test data
      await NATORank.create([
        { code: 'OR-1', description: 'Private' },
        { code: 'OR-2', description: 'Corporal' },
        { code: 'OF-1', description: 'Lieutenant' }
      ]);

      const res = await request(app)
        .get('/api/v1/nato-ranks?page=1&limit=2')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toHaveLength(2);
      expect(res.body.pagination.totalItems).toBe(3);
      expect(res.body.pagination.totalPages).toBe(2);
    });
  });

  describe('GET /api/v1/nato-ranks/:id', () => {
    test('should get NATO rank by ID', async () => {
      const natoRank = await NATORank.create({
        code: 'OR-1',
        description: 'Private'
      });

      const res = await request(app)
        .get(`/api/v1/nato-ranks/${natoRank._id}`)
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.code).toBe('OR-1');
      expect(res.body.data.description).toBe('Private');
    });

    test('should return 404 for non-existent NATO rank', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      
      const res = await request(app)
        .get(`/api/v1/nato-ranks/${fakeId}`)
        .expect(404);

      expect(res.body.success).toBe(false);
      expect(res.body.message).toBe('NATO rank not found');
    });

    test('should return 400 for invalid ID format', async () => {
      const res = await request(app)
        .get('/api/v1/nato-ranks/invalid-id')
        .expect(400);

      expect(res.body.error).toBe('Validation Error');
    });
  });

  describe('GET /api/v1/nato-ranks/code/:code', () => {
    test('should get NATO rank by code', async () => {
      await NATORank.create({
        code: 'OR-1',
        description: 'Private'
      });

      const res = await request(app)
        .get('/api/v1/nato-ranks/code/or-1') // Test case insensitive
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.code).toBe('OR-1');
    });

    test('should return 404 for non-existent code', async () => {
      const res = await request(app)
        .get('/api/v1/nato-ranks/code/XX-99')
        .expect(404);

      expect(res.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/nato-ranks', () => {
    test('should create new NATO rank', async () => {
      const natoRankData = {
        code: 'OR-1',
        description: 'Private'
      };

      const res = await request(app)
        .post('/api/v1/nato-ranks')
        .send(natoRankData)
        .expect(201);

      expect(res.body.success).toBe(true);
      expect(res.body.data.code).toBe('OR-1');
      expect(res.body.data.description).toBe('Private');
    });

    test('should validate required fields', async () => {
      const res = await request(app)
        .post('/api/v1/nato-ranks')
        .send({})
        .expect(400);

      expect(res.body.error).toBe('Validation Error');
    });

    test('should prevent duplicate codes', async () => {
      await NATORank.create({ code: 'OR-1', description: 'Private' });

      const res = await request(app)
        .post('/api/v1/nato-ranks')
        .send({ code: 'OR-1', description: 'Another Private' })
        .expect(409);

      expect(res.body.error).toBe('Duplicate Error');
    });
  });

  describe('PUT /api/v1/nato-ranks/:id', () => {
    test('should update NATO rank', async () => {
      const natoRank = await NATORank.create({
        code: 'OR-1',
        description: 'Private'
      });

      const res = await request(app)
        .put(`/api/v1/nato-ranks/${natoRank._id}`)
        .send({ description: 'Updated Private' })
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.description).toBe('Updated Private');
    });

    test('should return 404 for non-existent NATO rank', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      
      const res = await request(app)
        .put(`/api/v1/nato-ranks/${fakeId}`)
        .send({ description: 'Updated' })
        .expect(404);

      expect(res.body.success).toBe(false);
    });
  });

  describe('DELETE /api/v1/nato-ranks/:id', () => {
    test('should delete NATO rank when not referenced', async () => {
      const natoRank = await NATORank.create({
        code: 'OR-1',
        description: 'Private'
      });

      const res = await request(app)
        .delete(`/api/v1/nato-ranks/${natoRank._id}`)
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.message).toBe('NATO rank deleted successfully');
    });

    test('should prevent deletion when referenced by ranks', async () => {
      const natoRank = await NATORank.create({
        code: 'OR-1',
        description: 'Private'
      });

      await Rank.create({
        name: 'Soldado',
        level: 1,
        NATORank: natoRank._id
      });

      const res = await request(app)
        .delete(`/api/v1/nato-ranks/${natoRank._id}`)
        .expect(409);

      expect(res.body.error).toBe('Conflict');
      expect(res.body.message).toContain('being used by military ranks');
    });
  });

  describe('GET /api/v1/nato-ranks/stats', () => {
    test('should get NATO rank statistics', async () => {
      await NATORank.create([
        { code: 'OR-1', description: 'Private' },
        { code: 'OR-2', description: 'Corporal' },
        { code: 'OF-1', description: 'Lieutenant' }
      ]);

      const res = await request(app)
        .get('/api/v1/nato-ranks/stats')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.total).toBe(3);
      expect(res.body.data.byType.enlisted).toBe(2); // OR ranks
      expect(res.body.data.byType.officers).toBe(1);  // OF ranks
    });
  });
});

describe('Ranks API', () => {
  let natoRank;

  beforeEach(async () => {
    // Create a NATO rank for testing relationships
    natoRank = await NATORank.create({
      code: 'OR-1',
      description: 'Private'
    });
  });

  describe('GET /api/v1/ranks', () => {
    test('should get empty list when no ranks exist', async () => {
      const res = await request(app)
        .get('/api/v1/ranks')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toEqual([]);
      expect(res.body.pagination.totalItems).toBe(0);
    });

    test('should get paginated ranks with NATO rank population', async () => {
      await Rank.create([
        { name: 'Soldado', level: 1, NATORank: natoRank._id },
        { name: 'Cabo', level: 2, NATORank: natoRank._id },
        { name: 'Sargento', level: 3 }
      ]);

      const res = await request(app)
        .get('/api/v1/ranks?page=1&limit=2')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toHaveLength(2);
      expect(res.body.pagination.totalItems).toBe(3);
      expect(res.body.data[0].NATORank).toBeDefined();
      expect(res.body.data[0].NATORank.code).toBe('OR-1');
    });

    test('should filter ranks by level', async () => {
      await Rank.create([
        { name: 'Soldado', level: 1 },
        { name: 'Cabo', level: 2 },
        { name: 'Sargento', level: 2 }
      ]);

      const res = await request(app)
        .get('/api/v1/ranks?level=2')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toHaveLength(2);
      expect(res.body.data.every(rank => rank.level === 2)).toBe(true);
    });

    test('should filter ranks by NATO rank', async () => {
      await Rank.create([
        { name: 'Soldado', level: 1, NATORank: natoRank._id },
        { name: 'Cabo', level: 2 }
      ]);

      const res = await request(app)
        .get(`/api/v1/ranks?natoRank=${natoRank._id}`)
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toHaveLength(1);
      expect(res.body.data[0].name).toBe('Soldado');
    });
  });

  describe('GET /api/v1/ranks/:id', () => {
    test('should get rank by ID with NATO rank population', async () => {
      const rank = await Rank.create({
        name: 'Soldado',
        level: 1,
        abbreviation: 'SLD',
        NATORank: natoRank._id
      });

      const res = await request(app)
        .get(`/api/v1/ranks/${rank._id}`)
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.name).toBe('Soldado');
      expect(res.body.data.NATORank.code).toBe('OR-1');
    });

    test('should return 404 for non-existent rank', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const res = await request(app)
        .get(`/api/v1/ranks/${fakeId}`)
        .expect(404);

      expect(res.body.success).toBe(false);
      expect(res.body.message).toBe('Rank not found');
    });
  });

  describe('GET /api/v1/ranks/level/:level', () => {
    test('should get ranks by level', async () => {
      await Rank.create([
        { name: 'Soldado', level: 1, NATORank: natoRank._id },
        { name: 'Cabo', level: 2 },
        { name: 'Sargento', level: 2 }
      ]);

      const res = await request(app)
        .get('/api/v1/ranks/level/2')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data).toHaveLength(2);
      expect(res.body.count).toBe(2);
    });

    test('should validate level parameter', async () => {
      const res = await request(app)
        .get('/api/v1/ranks/level/invalid')
        .expect(400);

      expect(res.body.error).toBe('Invalid Level');
    });
  });

  describe('POST /api/v1/ranks', () => {
    test('should create new rank', async () => {
      const rankData = {
        name: 'Soldado',
        level: 1,
        abbreviation: 'SLD',
        NATORank: natoRank._id
      };

      const res = await request(app)
        .post('/api/v1/ranks')
        .send(rankData)
        .expect(201);

      expect(res.body.success).toBe(true);
      expect(res.body.data.name).toBe('Soldado');
      expect(res.body.data.abbreviation).toBe('SLD');
      expect(res.body.data.NATORank.code).toBe('OR-1');
    });

    test('should create rank without NATO rank reference', async () => {
      const rankData = {
        name: 'Soldado',
        level: 1
      };

      const res = await request(app)
        .post('/api/v1/ranks')
        .send(rankData)
        .expect(201);

      expect(res.body.success).toBe(true);
      expect(res.body.data.name).toBe('Soldado');
      expect(res.body.data.NATORank).toBeUndefined();
    });

    test('should validate required fields', async () => {
      const res = await request(app)
        .post('/api/v1/ranks')
        .send({})
        .expect(400);

      expect(res.body.error).toBe('Validation Error');
    });

    test('should validate NATO rank reference exists', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const res = await request(app)
        .post('/api/v1/ranks')
        .send({
          name: 'Soldado',
          level: 1,
          NATORank: fakeId
        })
        .expect(400);

      expect(res.body.error).toBe('Invalid Reference');
    });

    test('should prevent duplicate names', async () => {
      await Rank.create({ name: 'Soldado', level: 1 });

      const res = await request(app)
        .post('/api/v1/ranks')
        .send({ name: 'Soldado', level: 2 })
        .expect(409);

      expect(res.body.error).toBe('Duplicate Error');
    });
  });

  describe('PUT /api/v1/ranks/:id', () => {
    test('should update rank', async () => {
      const rank = await Rank.create({
        name: 'Soldado',
        level: 1
      });

      const res = await request(app)
        .put(`/api/v1/ranks/${rank._id}`)
        .send({
          name: 'Soldado Actualizado',
          abbreviation: 'SLD'
        })
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.name).toBe('Soldado Actualizado');
      expect(res.body.data.abbreviation).toBe('SLD');
    });

    test('should return 404 for non-existent rank', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const res = await request(app)
        .put(`/api/v1/ranks/${fakeId}`)
        .send({ name: 'Updated' })
        .expect(404);

      expect(res.body.success).toBe(false);
    });
  });

  describe('DELETE /api/v1/ranks/:id', () => {
    test('should delete rank', async () => {
      const rank = await Rank.create({
        name: 'Soldado',
        level: 1
      });

      const res = await request(app)
        .delete(`/api/v1/ranks/${rank._id}`)
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.message).toBe('Rank deleted successfully');
    });

    test('should return 404 for non-existent rank', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const res = await request(app)
        .delete(`/api/v1/ranks/${fakeId}`)
        .expect(404);

      expect(res.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/ranks/stats', () => {
    test('should get rank statistics', async () => {
      await Rank.create([
        { name: 'Soldado', level: 1, NATORank: natoRank._id },
        { name: 'Cabo', level: 2, NATORank: natoRank._id },
        { name: 'Sargento', level: 3 }
      ]);

      const res = await request(app)
        .get('/api/v1/ranks/stats')
        .expect(200);

      expect(res.body.success).toBe(true);
      expect(res.body.data.total).toBe(3);
      expect(res.body.data.withNATORank).toBe(2);
      expect(res.body.data.withoutNATORank).toBe(1);
      expect(res.body.data.levelDistribution).toHaveLength(3);
    });
  });
});
