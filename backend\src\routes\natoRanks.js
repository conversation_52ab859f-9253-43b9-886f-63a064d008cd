const express = require('express');
const {
  getAllNATORanks,
  getNATORankById,
  getNATORankByCode,
  createNATORank,
  updateNATORank,
  deleteNATORank,
  getNATORankStats
} = require('../controllers/natoRankController');

const {
  validateNATORankCreate,
  validateNATORankUpdate,
  validateObjectId,
  validatePagination
} = require('../middleware/validation');

/**
 * NATO Ranks Routes
 * Sprint Backend: API de Rangos
 * 
 * Routes for NATO rank CRUD operations
 * Base path: /api/v1/nato-ranks
 */

const router = express.Router();

// GET /api/v1/nato-ranks/stats - Get statistics (must be before /:id)
router.get('/stats', getNATORankStats);

// GET /api/v1/nato-ranks/code/:code - Get by code (must be before /:id)
router.get('/code/:code', getNATORankByCode);

// GET /api/v1/nato-ranks - Get all NATO ranks with pagination
router.get('/', validatePagination, getAllNATORanks);

// GET /api/v1/nato-ranks/:id - Get single NATO rank by ID
router.get('/:id', validateObjectId, getNATORankById);

// POST /api/v1/nato-ranks - Create new NATO rank
router.post('/', validateNATORankCreate, createNATORank);

// PUT /api/v1/nato-ranks/:id - Update NATO rank
router.put('/:id', validateObjectId, validateNATORankUpdate, updateNATORank);

// DELETE /api/v1/nato-ranks/:id - Delete NATO rank
router.delete('/:id', validateObjectId, deleteNATORank);

module.exports = router;
