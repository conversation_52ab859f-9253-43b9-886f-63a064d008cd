const mongoose = require('mongoose');

/**
 * NATORank Model
 * Represents NATO standard rank codes
 *
 * Fields according to project.json:
 * - _id: ObjectId
 * - code: String, required, unique
 * - description: String
 * - createdAt: Date
 * - updatedAt: Date
 *
 * Indexes: ["code"]
 */
const NATORankSchema = new mongoose.Schema({
  code: {
    type: String,
    required: [true, 'NATO rank code is required'],
    unique: true,
    trim: true,
    uppercase: true,
    match: [/^[A-Z0-9-]+$/, 'NATO rank code must contain only uppercase letters, numbers, and hyphens']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  }
}, {
  timestamps: true, // Automatically adds createdAt and updatedAt
  collection: 'natoranks'
});

// Indexes
NATORankSchema.index({ code: 1 });

// Instance methods (none specified in project.json)

// Static methods
NATORankSchema.statics.findByCode = function(code) {
  return this.findOne({ code: code.toUpperCase() });
};

// Pre-save middleware
NATORankSchema.pre('save', function(next) {
  if (this.code) {
    this.code = this.code.toUpperCase();
  }
  next();
});

// Export model
const NATORank = mongoose.model('NATORank', NATORankSchema);

module.exports = NATORank;