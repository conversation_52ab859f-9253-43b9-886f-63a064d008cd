# Backend - Sistema de Gestión de Personal Militar

## Descripción

## Metodología de Desarrollo

### Proceso de Iteraciones Completas (3 Sprints Paralelos)

El proyecto sigue una metodología de **iteraciones completas** donde cada funcionalidad se desarrolla mediante **3 sprints paralelos** que se ejecutan simultáneamente:

#### Estructura de una Iteración Completa

```
ITERACIÓN COMPLETA: [Nueva Funcionalidad]
├── 🗄️ SPRINT 1: Base de Datos
│   ├── Entidad/Modelo de dominio
│   ├── Seeds y datos de prueba
│   ├── Tests de entidad y validaciones
│   └── Referencias y relaciones
│
├── ⚙️ SPRINT 2: Backend (API)
│   ├── Endpoints REST
│   ├── Controladores
│   ├── Tests de API
│   └── Validaciones de negocio
│
└── 🎨 SPRINT 3: Frontend (UI)
    ├── Componentes React
    ├── Integración con API
    ├── Tests de componentes
    └── Validación de UX completa
```

#### Definición de Iteración Completa
Una **iteración completa se considera terminada** cuando:
1. ✅ **Sprint BD**: Entidad implementada, seeds funcionando, tests pasando
2. ✅ **Sprint Backend**: API implementada, endpoints funcionando, tests pasando  
3. ✅ **Sprint Frontend**: UI implementada, integración funcionando, tests pasando
4. ✅ **Feedback integral**: Todo el flujo BD → Backend → Frontend funciona correctamente

#### Principio OCP (Open-Closed Principle)
- **Una vez completada una iteración, el código implementado NO debe modificarse**
- Las nuevas funcionalidades se añaden mediante extensión, no modificación
- La composición y estructura del sistema se extrae de los archivos de documentación
- Cada iteración es independiente y no afecta a las anteriores

#### Desarrollo en Paralelo
Los **3 sprints se ejecutan simultáneamente** para obtener feedback inmediato:
- **Problemas se detectan en cada capa** (BD, Backend, Frontend)
- **Validación completa** en cada nivel
- **Tests integrales** garantizan robustez
- **No hay funcionalidad "a medias"**

#### Archivos que se actualizan tras cada sprint exitoso

Después de completar cada sprint exitoso, se actualizan **3 archivos clave**:

1. **`seed-validated-collections.js`**
   - Script principal de población de base de datos
   - Se actualiza añadiendo la nueva entidad/colección validada
   - Mantiene el estado coherente de todas las colecciones validadas
   - Incluye comentarios sobre iteraciones completadas y próximas

2. **`project-current.json`**
   - Estado actual del proyecto tras la iteración
   - Detalles de entidades implementadas y validadas
   - Configuración de base de datos y seeds
   - Información de desarrollo y próximos pasos
   - Es una copia de `project.json` con los cambios relevantes y contiene los datos del proyecto real

3. **`README.md`** (este archivo)
   - Documentación actualizada del estado del proyecto
   - Lista de iteraciones completadas
   - Estado actual de scripts y colecciones
   - Próximas iteraciones planificadas

#### Flujo de trabajo por sprint

```
SPRINT BD:
1. Implementar entidad + seeds + tests
   ↓
2. Actualizar seed-validated-collections.js
   ↓
3. Actualizar project-current.json
   ↓
4. Actualizar README.md
   ↓
5. Sprint BD terminado ✅

SPRINT BACKEND:
1. Implementar API + endpoints + tests
   ↓
2. Actualizar seed-validated-collections.js
   ↓
3. Actualizar project-current.json
   ↓
4. Actualizar README.md
   ↓
5. Sprint Backend terminado ✅

SPRINT FRONTEND:
1. Implementar UI + integración + tests
   ↓
2. Actualizar seed-validated-collections.js
   ↓
3. Actualizar project-current.json
   ↓
4. Actualizar README.md
   ↓
5. Sprint Frontend terminado ✅

ITERACIÓN COMPLETA:
6. Validar flujo completo BD → Backend → Frontend
   ↓
7. Iteración completa terminada ✅
```

#### Información extraíble de los archivos

Los 3 archivos proporcionan información completa sobre:

- **Composición del sistema**: Qué entidades están implementadas
- **Estructura de datos**: Cómo están organizadas las colecciones
- **Estado de validación**: Qué funcionalidades están probadas
- **Dependencias**: Relaciones entre entidades
- **Próximos pasos**: Roadmap de desarrollo

### Estado Actual del Proyecto

#### ✅ Lo que SÍ hemos completado:

**Base de Datos (BD):**
- ✅ **Sprint BD: Entidades de Rangos** - COMPLETADO
  - Modelo NATORank implementado y validado
  - Modelo Rank implementado y validado
  - Seeds con datos realistas de rangos militares
  - Tests completos para ambos modelos
  - Configuración de base de datos
  - Script de población validada

**Backend (Parcial):**
- ✅ Estructura del monorepo configurada
- ✅ Configuración de dependencias y scripts
- ✅ Configuración de tests con Jest
- ⏳ **Sprint Backend: API de Rangos** - PENDIENTE

**Frontend:**
- ⏳ **Sprint Frontend: UI de Rangos** - PENDIENTE

#### 📊 Estado Real del Proyecto:

```
PROYECTO ACTUAL:
├── ✅ Monorepo configurado (backend + frontend)
├── ✅ Sprint BD: Entidades de Rangos - COMPLETADO Y VALIDADO
│   ├── ✅ NATORank model (19 códigos NATO estándar)
│   ├── ✅ Rank model (21 rangos militares españoles)
│   ├── ✅ Seeds con datos realistas funcionando
│   ├── ✅ Tests completos (18 test cases - 100% coverage)
│   └── ✅ Configuración de BD funcionando
├── ✅ Sprint Backend: API de Rangos - COMPLETADO Y VALIDADO
│   ├── ✅ Servidor Express configurado y funcionando
│   ├── ✅ Controladores completos (NATO Ranks + Ranks)
│   ├── ✅ Rutas REST con validación (Zod)
│   ├── ✅ Middleware de validación y manejo de errores
│   ├── ✅ Tests de API completos (51 tests pasando)
│   └── ✅ Endpoints probados y funcionando
└── ⏳ Sprint Frontend: UI de Rangos (SIGUIENTE)

COLECCIONES VALIDADAS Y FUNCIONANDO:
- natoranks (19 documentos) ✅ PROBADO
- ranks (21 documentos con referencias NATO) ✅ PROBADO

API ENDPOINTS FUNCIONANDO:
- GET/POST/PUT/DELETE /api/v1/nato-ranks ✅ PROBADO
- GET/POST/PUT/DELETE /api/v1/ranks ✅ PROBADO
```

#### 🔄 Próximas Iteraciones Completas:

**INMEDIATO:**
1. **Sprint Backend: API de Rangos** - Endpoints REST para NATORank y Rank
2. **Sprint Frontend: UI de Rangos** - Interfaz Excel-like para gestión de rangos

**SIGUIENTE ITERACIÓN:**
3. **Sistema de Unidades** - Entidades Unit, UnitCategory, UnitType


## Estructura del Proyecto

```
Guard_augment_2/ (monorepo)
├── README.md
├── project.json (especificación completa)
├── project-current.json (estado actual - pendiente)
├── package.json (scripts del monorepo)
├── .gitignore
├── backend/
│   ├── package.json
│   ├── .env.example
│   ├── jest.config.js
│   ├── src/
│   │   ├── models/
│   │   │   ├── NATORank.js ✅
│   │   │   └── Rank.js ✅
│   │   ├── config/
│   │   │   └── database.js ✅
│   │   └── [controllers, routes, middleware, services, utils]
│   ├── scripts/
│   │   └── seed-validated-collections.js ✅
│   ├── data/
│   │   ├── nato-ranks.json ✅ (20 códigos NATO)
│   │   └── ranks.json ✅ (19 rangos militares)
│   ├── tests/
│   │   ├── setup.js ✅
│   │   └── models.test.js ✅ (24 test cases)
│   └── logs/
├── frontend/ (pendiente)
└── docs/
```

## Instalación

1. **Instalar dependencias del monorepo:**
   ```bash
   npm install
   npm run install:all
   ```

2. **Configurar variables de entorno:**
   ```bash
   cd backend
   cp .env.example .env
   # Editar .env con tus configuraciones
   ```

3. **Iniciar MongoDB:**
   - Asegúrate de tener MongoDB corriendo en `localhost:27017`
   - O configura `MONGODB_URI` en tu archivo `.env`

## Uso

### Desarrollo
```bash
# Ejecutar backend y frontend simultáneamente
npm run dev

# Solo backend
npm run dev:backend

# Solo frontend (cuando esté implementado)
npm run dev:frontend
```

### Tests
```bash
# Ejecutar todos los tests
npm test

# Solo tests del backend
npm run test:backend

# Tests con coverage
cd backend && npm run test:coverage
```

**Estado actual de tests:**
- ✅ **Tests de modelos**: 18 tests pasando (100% coverage)
- ✅ **Tests de API**: 33 tests pasando (100% coverage)
- ✅ **Total**: 51 tests pasando

### Seeds
```bash
# Poblar base de datos con datos validados
npm run seed
```

### Producción
```bash
npm start
```

## 🚀 API Endpoints

### Servidor
```bash
# Iniciar servidor de desarrollo
cd backend && npm run dev
# Servidor disponible en: http://localhost:3001
```

### Health Check & Info
- `GET /health` - Estado del servicio y base de datos
- `GET /` - Información básica de la API

### NATO Ranks API (`/api/v1/nato-ranks`)
- `GET /` - Listar rangos NATO (paginado)
- `GET /:id` - Obtener por ID
- `GET /code/:code` - Obtener por código
- `GET /stats` - Estadísticas
- `POST /` - Crear nuevo
- `PUT /:id` - Actualizar
- `DELETE /:id` - Eliminar

### Military Ranks API (`/api/v1/ranks`)
- `GET /` - Listar rangos (paginado, con filtros)
- `GET /:id` - Obtener por ID
- `GET /level/:level` - Obtener por nivel
- `GET /nato/:natoRankId` - Obtener por rango NATO
- `GET /stats` - Estadísticas
- `POST /` - Crear nuevo
- `PUT /:id` - Actualizar
- `DELETE /:id` - Eliminar

**Todos los endpoints están probados y funcionando** ✅

## Configuración

### Variables de Entorno

| Variable | Descripción | Default |
|----------|-------------|---------|
| `PORT` | Puerto del servidor | `3001` |
| `NODE_ENV` | Ambiente | `development` |
| `MONGODB_URI` | URI de MongoDB | `mongodb://localhost:27017/personnel_management2` |
| `JWT_SECRET` | Secreto para JWT | `default_jwt_secret_change_in_production` |
| `CORS_ORIGIN` | Origen permitido para CORS | `http://localhost:3000` |
| `LOG_LEVEL` | Nivel de logging | `info` |

## Logging

La aplicación utiliza Winston para logging con los siguientes niveles:
- **Error**: Errores críticos
- **Info**: Información general
- **Debug**: Información detallada (solo en desarrollo)

Los logs se guardan en:
- `logs/error.log` - Solo errores
- `logs/combined.log` - Todos los logs

## Script de Población de Colecciones Validadas

Para garantizar que la base de datos siempre refleje el estado validado y coherente del proyecto tras cada iteración, utiliza el siguiente script:

### Población incremental validada

```bash
# Ejecutar script de seeds validados
npm run seed

# O directamente desde backend
cd backend && npm run seed
```

Este script:
- Elimina todas las colecciones existentes en la base de datos (evita residuos de pruebas o iteraciones anteriores).
- Inserta únicamente los datos de las entidades y colecciones que ya han sido validadas y confirmadas en el desarrollo.
- Deja la base de datos exactamente en el estado correcto hasta la última iteración validada.
- Se debe actualizar tras cada iteración exitosa, añadiendo la lógica y los datos de la nueva entidad validada.

#### Estado actual del script (Sprint BD: Entidades de Rangos - COMPLETADO Y VALIDADO)
- ✅ **NATORank**: 19 códigos NATO estándar (OR-1 a OR-9, OF-1 a OF-10) - FUNCIONANDO
- ✅ **Rank**: 21 rangos militares españoles con referencias NATO - FUNCIONANDO
- ✅ **Validación**: 18 tests completos (100% coverage) - PASANDO
- ✅ **Métodos**: findByCode(), findByLevel(), getNATOCode(), etc. - PROBADOS
- ✅ **Seeds**: Script ejecutado exitosamente - 40 documentos totales

#### Estructura jerárquica de unidades para incluir en los seeds

```
Tercio (unidad raíz)
├── Batallón 1
│   ├── Compañía 1 → Secciones 1, 2, 3 + Plana Mayor
│   ├── Compañía 2 → Secciones 1, 2, 3 + Plana Mayor
│   ├── Compañía 3 → Secciones 1, 2, 3 + Plana Mayor
│   └── Compañía de Plana Mayor → Secciones 1, 2, 3 + Plana Mayor
├── Batallón 2
│   ├── Compañía 4 → Secciones 1, 2, 3 + Plana Mayor
│   ├── Compañía 5 → Secciones 1, 2, 3 + Plana Mayor
│   ├── Compañía 6 → Secciones 1, 2, 3 + Plana Mayor
│   └── Compañía de Plana Mayor → Secciones 1, 2, 3 + Plana Mayor
└── Batallón de Estado Mayor
    └── Compañía de Estado Mayor → Secciones 1, 2, 3 + Plana Mayor
```

#### Sistema de referencias por `_id` 

- Cuando se vaya a crear el script para el seed de la base de datos hay que tener en cuenta que existen datos que deben de ir referenciando a otros datos que ya existen en la base de datos, pero que aun no se encuentran creados. Por ejemplo, cuando se crea el seed de las `Units` se debe de referenciar a las `UnitCategories` que ya existen, pero que aun no se encuentran creadas. Para solventar este problema, se debe de crear un sistema de referencias por `_id` que permita referenciar a los datos que aun no se encuentran creados (atrapar esos `_id` para luego colocarlos en donde haga falta)

#### Datasets individuales disponibles

Cada entidad tendra su propio dataset individual que será utilizado para poblar la base de datos. Estos datasets se encuentran en la carpeta `data/` y se encuentran en formato `json`.

```bash
backend/data/
├── nato-ranks.json ✅ (19 códigos NATO estándar) - VALIDADO
└── ranks.json ✅ (21 rangos militares españoles) - VALIDADO

# Próximos datasets (siguientes iteraciones):
├── unit-categories.json (pendiente)
├── unit-types.json (pendiente)
└── units.json (pendiente)
```

#### Próximos pasos
Cuando completes y valides una nueva entidad (por ejemplo, Personnel), añade su lógica y datos a este script y actualiza esta sección del README. Así, siempre podrás restaurar la base de datos a un estado seguro y validado para continuar el desarrollo o las pruebas.

## Próximas Iteraciones

1. **Implementar entidad Personnel**: Personal militar individual con referencias a todas las entidades
2. **API REST**: Endpoints para todas las entidades
3. **Sistema de autenticación** (JWT)
4. **CRUD completo** para entidades principales
5. **Sistema de eventos y guardias**

## Notas de Desarrollo

- `project.json` es el archivo principal de configuración del proyecto es una guia de todo el proyecto, pero no contiene los datos finales del proyecto. Es una guía que se irá actualizando a medida que se vaya desarrollando el proyecto en el archivo `project-current.json`
- `project-current.json` es una copia de `project.json` con los cambios finales de la implementacion del proyecto.
- La carpeta `temp/` contiene implementaciones temporales para testing para no dejar residuos en el código. Los test oficiales no deben de depender de esta carpeta
- Esta carpeta debe ser eliminada antes del deployment final
- Ver `temp/README.md` para más detalles
- Todas las entidades incluyen validaciones de dominio y métodos de negocio.