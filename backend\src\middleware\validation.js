const { z } = require('zod');

/**
 * Validation Middleware
 * Sprint Backend: API de Rangos
 * 
 * Zod schemas and validation middleware for API endpoints
 */

// NATO Rank validation schemas
const natoRankSchemas = {
  create: z.object({
    code: z.string()
      .min(1, 'NATO rank code is required')
      .max(10, 'NATO rank code cannot exceed 10 characters')
      .regex(/^[A-Z0-9-]+$/, 'NATO rank code must contain only uppercase letters, numbers, and hyphens')
      .transform(val => val.toUpperCase()),
    description: z.string()
      .max(200, 'Description cannot exceed 200 characters')
      .optional()
  }),

  update: z.object({
    code: z.string()
      .min(1, 'NATO rank code is required')
      .max(10, 'NATO rank code cannot exceed 10 characters')
      .regex(/^[A-Z0-9-]+$/, 'NATO rank code must contain only uppercase letters, numbers, and hyphens')
      .transform(val => val.toUpperCase())
      .optional(),
    description: z.string()
      .max(200, 'Description cannot exceed 200 characters')
      .optional()
  })
};

// Rank validation schemas
const rankSchemas = {
  create: z.object({
    name: z.string()
      .min(1, 'Rank name is required')
      .max(100, 'Rank name cannot exceed 100 characters')
      .trim(),
    level: z.number()
      .int('Level must be an integer')
      .min(1, 'Rank level must be at least 1')
      .max(50, 'Rank level cannot exceed 50'),
    abbreviation: z.string()
      .max(10, 'Abbreviation cannot exceed 10 characters')
      .transform(val => val ? val.toUpperCase() : val)
      .optional(),
    NATORank: z.string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid NATORank ID format')
      .optional()
  }),

  update: z.object({
    name: z.string()
      .min(1, 'Rank name is required')
      .max(100, 'Rank name cannot exceed 100 characters')
      .trim()
      .optional(),
    level: z.number()
      .int('Level must be an integer')
      .min(1, 'Rank level must be at least 1')
      .max(50, 'Rank level cannot exceed 50')
      .optional(),
    abbreviation: z.string()
      .max(10, 'Abbreviation cannot exceed 10 characters')
      .transform(val => val ? val.toUpperCase() : val)
      .optional(),
    NATORank: z.string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid NATORank ID format')
      .optional()
  })
};

// Query parameter schemas
const querySchemas = {
  pagination: z.object({
    page: z.string()
      .regex(/^\d+$/, 'Page must be a positive integer')
      .transform(val => parseInt(val))
      .refine(val => val > 0, 'Page must be greater than 0')
      .optional()
      .default('1'),
    limit: z.string()
      .regex(/^\d+$/, 'Limit must be a positive integer')
      .transform(val => parseInt(val))
      .refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100')
      .optional()
      .default('10')
  }),

  rankFilters: z.object({
    level: z.string()
      .regex(/^\d+$/, 'Level must be a positive integer')
      .transform(val => parseInt(val))
      .optional(),
    natoRank: z.string()
      .regex(/^[0-9a-fA-F]{24}$/, 'Invalid NATORank ID format')
      .optional()
  })
};

// MongoDB ObjectId validation
const objectIdSchema = z.string()
  .regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format');

/**
 * Generic validation middleware factory
 */
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    try {
      const data = source === 'body' ? req.body : 
                   source === 'params' ? req.params :
                   source === 'query' ? req.query : req[source];

      const result = schema.safeParse(data);

      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }));

        return res.status(400).json({
          error: 'Validation Error',
          message: 'Invalid data provided',
          details: errors
        });
      }

      // Replace the original data with validated/transformed data
      if (source === 'body') req.body = result.data;
      else if (source === 'params') req.params = result.data;
      else if (source === 'query') req.query = result.data;
      else req[source] = result.data;

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Specific validation middleware functions
 */
const validateNATORankCreate = validate(natoRankSchemas.create);
const validateNATORankUpdate = validate(natoRankSchemas.update);
const validateRankCreate = validate(rankSchemas.create);
const validateRankUpdate = validate(rankSchemas.update);
const validateObjectId = validate(z.object({ id: objectIdSchema }), 'params');
const validatePagination = validate(querySchemas.pagination, 'query');
const validateRankFilters = validate(querySchemas.rankFilters.merge(querySchemas.pagination), 'query');

module.exports = {
  // Schemas
  natoRankSchemas,
  rankSchemas,
  querySchemas,
  objectIdSchema,
  
  // Middleware functions
  validate,
  validateNATORankCreate,
  validateNATORankUpdate,
  validateRankCreate,
  validateRankUpdate,
  validateObjectId,
  validatePagination,
  validateRankFilters
};
