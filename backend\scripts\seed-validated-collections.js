const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Import models
const NATORank = require('../src/models/NATORank');
const Rank = require('../src/models/Rank');

/**
 * Seed script for validated collections
 * Sprint BD: Entidades de Rangos
 *
 * This script:
 * 1. Connects to MongoDB
 * 2. Seeds NATORank collection with NATO standard codes
 * 3. Seeds Rank collection with military ranks linked to NATO codes
 * 4. Validates all data integrity
 * 5. Reports seeding results
 */

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/personnel_management2';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function loadJSONData(filename) {
  try {
    const filePath = path.join(__dirname, '../data', filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ Error loading ${filename}:`, error);
    throw error;
  }
}

async function seedNATORanks() {
  console.log('\n🌱 Seeding NATO Ranks...');

  try {
    // Clear existing data
    await NATORank.deleteMany({});
    console.log('   Cleared existing NATO ranks');

    // Load data
    const natoRanksData = await loadJSONData('nato-ranks.json');

    // Insert data
    const natoRanks = await NATORank.insertMany(natoRanksData);
    console.log(`   ✅ Inserted ${natoRanks.length} NATO ranks`);

    // Validate data
    const count = await NATORank.countDocuments();
    console.log(`   📊 Total NATO ranks in DB: ${count}`);

    return natoRanks;
  } catch (error) {
    console.error('❌ Error seeding NATO ranks:', error);
    throw error;
  }
}

async function seedRanks(natoRanks) {
  console.log('\n🌱 Seeding Military Ranks...');

  try {
    // Clear existing data
    await Rank.deleteMany({});
    console.log('   Cleared existing ranks');

    // Load data
    const ranksData = await loadJSONData('ranks.json');

    // Create NATO code to ObjectId mapping
    const natoCodeMap = {};
    natoRanks.forEach(natoRank => {
      natoCodeMap[natoRank.code] = natoRank._id;
    });

    // Transform data to include NATORank ObjectId references
    const transformedRanks = ranksData.map(rank => ({
      name: rank.name,
      level: rank.level,
      abbreviation: rank.abbreviation,
      NATORank: natoCodeMap[rank.natoCode] || null
    }));

    // Insert data
    const ranks = await Rank.insertMany(transformedRanks);
    console.log(`   ✅ Inserted ${ranks.length} military ranks`);

    // Validate data
    const count = await Rank.countDocuments();
    const withNATO = await Rank.countDocuments({ NATORank: { $ne: null } });
    console.log(`   📊 Total ranks in DB: ${count}`);
    console.log(`   📊 Ranks with NATO reference: ${withNATO}`);

    return ranks;
  } catch (error) {
    console.error('❌ Error seeding ranks:', error);
    throw error;
  }
}

async function validateData() {
  console.log('\n🔍 Validating seeded data...');

  try {
    // Test NATORank model methods
    const testNATO = await NATORank.findByCode('OR-1');
    console.log(`   ✅ NATORank.findByCode() works: ${testNATO ? testNATO.code : 'Not found'}`);

    // Test Rank model methods
    const testRank = await Rank.findByLevel(1);
    console.log(`   ✅ Rank.findByLevel() works: Found ${testRank.length} rank(s) at level 1`);

    // Test population
    const rankWithNATO = await Rank.findOne({ NATORank: { $ne: null } }).populate('NATORank');
    if (rankWithNATO && rankWithNATO.NATORank) {
      console.log(`   ✅ Population works: ${rankWithNATO.name} -> ${rankWithNATO.NATORank.code}`);
    }

    // Test instance method
    if (rankWithNATO) {
      const natoCode = await rankWithNATO.getNATOCode();
      console.log(`   ✅ Instance method works: getNATOCode() -> ${natoCode}`);
    }

    console.log('   ✅ All validations passed');
  } catch (error) {
    console.error('❌ Validation error:', error);
    throw error;
  }
}

async function generateReport() {
  console.log('\n📋 Final Report:');
  console.log('================');

  const natoCount = await NATORank.countDocuments();
  const rankCount = await Rank.countDocuments();
  const linkedRanks = await Rank.countDocuments({ NATORank: { $ne: null } });

  console.log(`NATO Ranks: ${natoCount}`);
  console.log(`Military Ranks: ${rankCount}`);
  console.log(`Linked Ranks: ${linkedRanks}`);
  console.log(`Unlinked Ranks: ${rankCount - linkedRanks}`);

  // Show sample data
  console.log('\n📋 Sample Data:');
  const sampleRanks = await Rank.find().populate('NATORank').limit(3);
  sampleRanks.forEach(rank => {
    const natoCode = rank.NATORank ? rank.NATORank.code : 'No NATO';
    console.log(`   ${rank.name} (${rank.abbreviation}) - Level ${rank.level} - NATO: ${natoCode}`);
  });
}

async function main() {
  console.log('🚀 Starting seed process for Sprint BD: Entidades de Rangos');
  console.log('===========================================================');

  try {
    await connectDB();

    const natoRanks = await seedNATORanks();
    const ranks = await seedRanks(natoRanks);

    await validateData();
    await generateReport();

    console.log('\n🎉 Seeding completed successfully!');
    console.log('Sprint BD: Entidades de Rangos - COMPLETED ✅');

  } catch (error) {
    console.error('\n💥 Seeding failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  seedNATORanks,
  seedRanks,
  validateData,
  generateReport
};