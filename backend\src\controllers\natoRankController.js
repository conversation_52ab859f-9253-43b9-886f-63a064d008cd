const NATORank = require('../models/NATORank');

/**
 * NATO Rank Controller
 * Sprint Backend: API de Rangos
 * 
 * CRUD operations for NATO Rank entities
 * Endpoints: GET, POST, PUT, DELETE /api/v1/nato-ranks
 */

/**
 * @desc    Get all NATO ranks with pagination
 * @route   GET /api/v1/nato-ranks
 * @access  Public (will be protected later)
 */
const getAllNATORanks = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const total = await NATORank.countDocuments();
    
    // Get paginated results
    const natoRanks = await NATORank.find()
      .sort({ code: 1 }) // Sort by code alphabetically
      .skip(skip)
      .limit(parseInt(limit));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      success: true,
      data: natoRanks,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get single NATO rank by ID
 * @route   GET /api/v1/nato-ranks/:id
 * @access  Public (will be protected later)
 */
const getNATORankById = async (req, res, next) => {
  try {
    const natoRank = await NATORank.findById(req.params.id);

    if (!natoRank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'NATO rank not found'
      });
    }

    res.status(200).json({
      success: true,
      data: natoRank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get NATO rank by code
 * @route   GET /api/v1/nato-ranks/code/:code
 * @access  Public (will be protected later)
 */
const getNATORankByCode = async (req, res, next) => {
  try {
    const natoRank = await NATORank.findByCode(req.params.code);

    if (!natoRank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'NATO rank not found'
      });
    }

    res.status(200).json({
      success: true,
      data: natoRank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Create new NATO rank
 * @route   POST /api/v1/nato-ranks
 * @access  Private (will be protected later)
 */
const createNATORank = async (req, res, next) => {
  try {
    const natoRank = await NATORank.create(req.body);

    res.status(201).json({
      success: true,
      message: 'NATO rank created successfully',
      data: natoRank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update NATO rank
 * @route   PUT /api/v1/nato-ranks/:id
 * @access  Private (will be protected later)
 */
const updateNATORank = async (req, res, next) => {
  try {
    const natoRank = await NATORank.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true, // Return updated document
        runValidators: true // Run schema validations
      }
    );

    if (!natoRank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'NATO rank not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'NATO rank updated successfully',
      data: natoRank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Delete NATO rank
 * @route   DELETE /api/v1/nato-ranks/:id
 * @access  Private (will be protected later)
 */
const deleteNATORank = async (req, res, next) => {
  try {
    const natoRank = await NATORank.findById(req.params.id);

    if (!natoRank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'NATO rank not found'
      });
    }

    // Check if NATO rank is being used by any ranks
    const Rank = require('../models/Rank');
    const ranksUsingThisNATO = await Rank.countDocuments({ NATORank: req.params.id });

    if (ranksUsingThisNATO > 0) {
      return res.status(409).json({
        success: false,
        error: 'Conflict',
        message: 'Cannot delete NATO rank as it is being used by military ranks',
        details: {
          ranksCount: ranksUsingThisNATO
        }
      });
    }

    await NATORank.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'NATO rank deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get NATO ranks statistics
 * @route   GET /api/v1/nato-ranks/stats
 * @access  Public (will be protected later)
 */
const getNATORankStats = async (req, res, next) => {
  try {
    const total = await NATORank.countDocuments();
    
    // Count by rank type (OR vs OF)
    const orRanks = await NATORank.countDocuments({ code: /^OR-/ });
    const ofRanks = await NATORank.countDocuments({ code: /^OF-/ });

    res.status(200).json({
      success: true,
      data: {
        total,
        byType: {
          enlisted: orRanks, // OR ranks (Other Ranks)
          officers: ofRanks   // OF ranks (Officers)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllNATORanks,
  getNATORankById,
  getNATORankByCode,
  createNATORank,
  updateNATORank,
  deleteNATORank,
  getNATORankStats
};
