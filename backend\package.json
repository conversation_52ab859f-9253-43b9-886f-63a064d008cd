{"name": "personnel-management-backend", "version": "1.0.0", "description": "Sistema de Gestión de Personal Militar - Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "node scripts/seed-validated-collections.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["personnel", "management", "military", "nodejs", "express", "mongodb"], "author": "KPLdeV", "license": "MIT", "dependencies": {"express": "^4.18.0", "mongoose": "^7.6.0", "jsonwebtoken": "^9.0.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "winston": "^3.8.0", "zod": "^3.22.0", "dotenv": "^16.3.0", "socket.io": "^4.7.0", "bullmq": "^5.0.0", "multer": "^1.4.5"}, "devDependencies": {"nodemon": "^3.0.0", "jest": "^29.7.0", "supertest": "^6.3.0", "eslint": "^8.50.0", "prettier": "^3.0.0", "@types/jest": "^29.5.0"}, "engines": {"node": ">=18.0.0"}}