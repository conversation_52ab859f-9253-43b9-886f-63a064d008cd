import { apiSlice } from '../../services/api';

/**
 * Ranks Testing API - Para pruebas de la iteración actual
 * NOTA: Este archivo será eliminado al finalizar el desarrollo
 * 
 * Endpoints para probar la funcionalidad de Rangos implementada
 */
export const ranksTestingApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Obtener todos los rangos
    getAllRanks: builder.query({
      query: () => '/ranks?limit=100', // Obtener hasta 100 rangos para testing
      transformResponse: (response) => response.data || [],
      providesTags: ['Rank'],
    }),

    // Obtener rango por ID
    getRankById: builder.query({
      query: (id) => `/ranks/${id}`,
      providesTags: (result, error, id) => [{ type: 'Rank', id }],
    }),

    // Crear nuevo rango
    createRank: builder.mutation({
      query: (newRank) => ({
        url: '/ranks',
        method: 'POST',
        body: newRank,
      }),
      invalidatesTags: ['Rank'],
    }),

    // Actualizar rango
    updateRank: builder.mutation({
      query: ({ id, ...patch }) => ({
        url: `/ranks/${id}`,
        method: 'PUT',
        body: patch,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Rank', id }],
    }),

    // Eliminar rango
    deleteRank: builder.mutation({
      query: (id) => ({
        url: `/ranks/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Rank'],
    }),

    // Obtener estadísticas de rangos
    getRankStats: builder.query({
      query: () => '/ranks/stats',
      transformResponse: (response) => response.data || {},
      providesTags: ['Rank'],
    }),

    // Obtener rangos NATO
    getNATORanks: builder.query({
      query: () => '/nato-ranks?limit=100', // Obtener hasta 100 rangos NATO para testing
      transformResponse: (response) => response.data || [],
      providesTags: ['NATORank'],
    }),

    // Buscar rangos
    searchRanks: builder.query({
      query: (searchParams) => ({
        url: '/ranks/search',
        params: searchParams,
      }),
      providesTags: ['Rank'],
    }),

    // Obtener rangos por país
    getRanksByCountry: builder.query({
      query: (country) => `/ranks/country/${encodeURIComponent(country)}`,
      providesTags: ['Rank'],
    }),

    // Obtener rangos por servicio
    getRanksByService: builder.query({
      query: (service) => `/ranks/service/${encodeURIComponent(service)}`,
      providesTags: ['Rank'],
    }),

    // Obtener rangos por categoría
    getRanksByCategory: builder.query({
      query: (category) => `/ranks/category/${encodeURIComponent(category)}`,
      providesTags: ['Rank'],
    }),
  }),
});

// Export hooks para usar en componentes
export const {
  useGetAllRanksQuery,
  useGetRankByIdQuery,
  useCreateRankMutation,
  useUpdateRankMutation,
  useDeleteRankMutation,
  useGetRankStatsQuery,
  useGetNATORanksQuery,
  useSearchRanksQuery,
  useGetRanksByCountryQuery,
  useGetRanksByServiceQuery,
  useGetRanksByCategoryQuery,
} = ranksTestingApi;
