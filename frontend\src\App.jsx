import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { store } from './store';
import { MainLayout } from './components/layout';

// Pages - will be created in next sprint
import HomePage from './pages/HomePage';
// import RanksPage from './pages/RanksPage';
// import UnitsPage from './pages/UnitsPage';

// Testing Area - TEMPORARY (will be removed after development)
import RanksTestingPage from './testing-area/pages/RanksTestingPage';
// import PersonnelPage from './pages/PersonnelPage';

/**
 * Main App Component
 * Follows OCP principle - extensible routing structure
 *
 * Features:
 * - React Router for navigation
 * - Redux Provider for state management
 * - Toast notifications
 * - Layout wrapper
 * - Route protection (ready for implementation)
 */
function App() {
  return (
    <Provider store={store}>
      <Router>
        <div className="App">
          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
                fontSize: '14px',
              },
              success: {
                style: {
                  background: '#22c55e',
                },
              },
              error: {
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />

          {/* Main application routes */}
          <Routes>
            {/* Home route */}
            <Route
              path="/"
              element={
                <MainLayout title="Dashboard">
                  <HomePage />
                </MainLayout>
              }
            />

            {/* Ranks routes - will be implemented in next sprint */}
            {/* <Route
              path="/rangos"
              element={
                <MainLayout
                  title="Gestión de Rangos"
                  breadcrumbs={[
                    { label: 'Personal' },
                    { label: 'Rangos' }
                  ]}
                >
                  <RanksPage />
                </MainLayout>
              }
            /> */}

            {/* Units routes - future implementation */}
            {/* <Route
              path="/unidades"
              element={
                <MainLayout
                  title="Gestión de Unidades"
                  breadcrumbs={[
                    { label: 'Personal' },
                    { label: 'Unidades' }
                  ]}
                >
                  <UnitsPage />
                </MainLayout>
              }
            /> */}

            {/* Personnel routes - future implementation */}
            {/* <Route
              path="/personal"
              element={
                <MainLayout
                  title="Gestión de Personal"
                  breadcrumbs={[
                    { label: 'Personal' },
                    { label: 'Personal' }
                  ]}
                >
                  <PersonnelPage />
                </MainLayout>
              }
            /> */}

            {/* Testing Area Routes - TEMPORARY (will be removed after development) */}
            <Route
              path="/testing/ranks"
              element={
                <MainLayout
                  title="Pruebas de Rangos"
                  breadcrumbs={[
                    { label: 'Área de Pruebas' },
                    { label: 'Rangos' }
                  ]}
                >
                  <RanksTestingPage />
                </MainLayout>
              }
            />

            {/* Catch all route - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </Provider>
  );
}

export default App;
