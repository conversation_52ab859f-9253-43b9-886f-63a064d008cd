import { useState } from 'react';
import clsx from 'clsx';

/**
 * Sidebar Component - Excel-like navigation sidebar
 * Follows OCP principle - extensible navigation structure
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Sidebar open state (mobile)
 * @param {Function} props.onClose - Close handler (mobile)
 */
const Sidebar = ({ isOpen, onClose }) => {
  const [expandedSections, setExpandedSections] = useState({
    personal: true,
    guardias: false,
    reportes: false,
    testing: true,
    configuracion: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const menuItems = [
    {
      id: 'personal',
      label: 'Personal',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      children: [
        { label: 'Rangos', href: '/rangos', active: true },
        { label: 'Unidades', href: '/unidades' },
        { label: 'Personal', href: '/personal' }
      ]
    },
    {
      id: 'guardias',
      label: 'Guardias',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      children: [
        { label: 'Programación', href: '/guardias/programacion' },
        { label: 'Asignaciones', href: '/guardias/asignaciones' },
        { label: 'Solicitudes', href: '/guardias/solicitudes' }
      ]
    },
    {
      id: 'reportes',
      label: 'Reportes',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      children: [
        { label: 'Personal', href: '/reportes/personal' },
        { label: 'Guardias', href: '/reportes/guardias' },
        { label: 'Estadísticas', href: '/reportes/estadisticas' }
      ]
    },
    {
      id: 'testing',
      label: 'Área de Pruebas',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      children: [
        { label: 'Pruebas Rangos', href: '/testing/ranks', badge: 'TEMP' }
      ]
    },
    {
      id: 'configuracion',
      label: 'Configuración',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      children: [
        { label: 'Sistema', href: '/configuracion/sistema' },
        { label: 'Usuarios', href: '/configuracion/usuarios' },
        { label: 'Permisos', href: '/configuracion/permisos' }
      ]
    }
  ];

  const sidebarClasses = clsx(
    'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
    {
      'translate-x-0': isOpen,
      '-translate-x-full': !isOpen
    }
  );

  return (
    <aside className={sidebarClasses}>
      <div className="flex flex-col h-full">
        {/* Sidebar header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
          <span className="text-lg font-semibold text-gray-900">Menú</span>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {menuItems.map((item) => (
            <div key={item.id}>
              <button
                onClick={() => toggleSection(item.id)}
                className="flex items-center justify-between w-full px-3 py-2 text-left text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
              >
                <div className="flex items-center">
                  <span className="text-gray-500">{item.icon}</span>
                  <span className="ml-3 text-sm font-medium">{item.label}</span>
                </div>
                <svg
                  className={clsx(
                    'w-4 h-4 text-gray-400 transition-transform',
                    expandedSections[item.id] ? 'rotate-90' : ''
                  )}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Submenu */}
              {expandedSections[item.id] && (
                <div className="mt-2 ml-8 space-y-1">
                  {item.children.map((child, index) => (
                    <a
                      key={index}
                      href={child.href}
                      className={clsx(
                        'flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors',
                        child.active
                          ? 'bg-primary-100 text-primary-700 font-medium'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      )}
                    >
                      <span>{child.label}</span>
                      {child.badge && (
                        <span className="px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          {child.badge}
                        </span>
                      )}
                    </a>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
