import { useState } from 'react';
import { Card, Button, Modal, Input, Select } from '../../components/common';
import TestingTable from '../components/TestingTable';
import {
  useGetAllRanksQuery,
  useGetNATORanksQuery,
  useCreateRankMutation,
  useUpdateRankMutation,
  useDeleteRankMutation,
  useGetRankStatsQuery,
} from '../services/ranksTestingApi';

/**
 * RanksTestingPage - Página de pruebas para Rangos
 * NOTA: Esta página será eliminada al finalizar el desarrollo
 * 
 * Interfaz para probar todos los endpoints y funcionalidades de Rangos
 */
const RanksTestingPage = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingRank, setEditingRank] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    abbreviation: '',
    level: '',
    NATORank: ''
  });

  // Queries
  const { data: ranks = [], error: ranksError, isLoading: ranksLoading, refetch: refetchRanks } = useGetAllRanksQuery();
  const { data: natoRanks = [], isLoading: natoLoading } = useGetNATORanksQuery();
  const { data: stats, isLoading: statsLoading } = useGetRankStatsQuery();

  // Mutations
  const [createRank, { isLoading: creating }] = useCreateRankMutation();
  const [updateRank, { isLoading: updating }] = useUpdateRankMutation();
  const [deleteRank, { isLoading: deleting }] = useDeleteRankMutation();

  // Configuración de columnas para la tabla
  const columns = [
    { key: 'name', header: 'Nombre' },
    { key: 'abbreviation', header: 'Abreviación' },
    { key: 'level', header: 'Nivel', render: (value) => value || 'N/A' },
    {
      key: 'NATORank',
      header: 'Grado NATO',
      render: (value) => value ? value.code : 'N/A'
    },
    {
      key: 'createdAt',
      header: 'Creado',
      render: (value) => new Date(value).toLocaleDateString('es-ES')
    }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const resetForm = () => {
    setFormData({
      name: '',
      abbreviation: '',
      level: '',
      NATORank: ''
    });
  };

  const handleCreate = async () => {
    try {
      // Preparar datos asegurando que level sea número
      const dataToSend = {
        ...formData,
        level: parseInt(formData.level) || 1,
        NATORank: formData.NATORank || undefined // No enviar si está vacío
      };
      console.log('Creating rank with data:', dataToSend);
      const result = await createRank(dataToSend).unwrap();
      console.log('Rank created successfully:', result);
      setShowCreateModal(false);
      resetForm();
      refetchRanks();
    } catch (error) {
      console.error('Error creating rank:', error);
      alert(`Error al crear rango: ${error.data?.message || error.message || 'Error desconocido'}`);
    }
  };

  const handleEdit = (rank) => {
    setEditingRank(rank);
    setFormData({
      name: rank.name || '',
      abbreviation: rank.abbreviation || '',
      level: rank.level || '',
      NATORank: rank.NATORank?._id || ''
    });
    setShowEditModal(true);
  };

  const handleUpdate = async () => {
    try {
      await updateRank({ id: editingRank._id, ...formData }).unwrap();
      setShowEditModal(false);
      setEditingRank(null);
      resetForm();
      refetchRanks();
    } catch (error) {
      console.error('Error updating rank:', error);
    }
  };

  const handleDelete = async (rank) => {
    if (window.confirm(`¿Estás seguro de eliminar el rango "${rank.name}"?`)) {
      try {
        await deleteRank(rank._id).unwrap();
        refetchRanks();
      } catch (error) {
        console.error('Error deleting rank:', error);
      }
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h2 className="text-lg font-semibold text-yellow-800">Área de Pruebas - Iteración Rangos</h2>
            <p className="text-sm text-yellow-700">
              Esta interfaz será eliminada al finalizar el desarrollo. Úsala para probar la funcionalidad implementada.
            </p>
          </div>
        </div>
      </div>

      {/* Stats */}
      {!statsLoading && stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="text-center">
            <div className="text-2xl font-bold text-primary-600 mb-1">{stats.totalRanks || 0}</div>
            <div className="text-sm text-gray-600">Total Rangos</div>
          </Card>
          <Card className="text-center">
            <div className="text-2xl font-bold text-accent-600 mb-1">{stats.totalNATORanks || 0}</div>
            <div className="text-sm text-gray-600">Rangos NATO</div>
          </Card>
          <Card className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats.ranksWithNATO || 0}</div>
            <div className="text-sm text-gray-600">Con Equivalencia NATO</div>
          </Card>
        </div>
      )}

      {/* Tabla de Rangos */}
      <TestingTable
        title="Rangos Militares"
        data={ranks}
        columns={columns}
        loading={ranksLoading}
        error={ranksError}
        onRefresh={refetchRanks}
        onAdd={() => setShowCreateModal(true)}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Tabla de Rangos NATO */}
      <TestingTable
        title="Rangos NATO (Solo Lectura)"
        data={natoRanks}
        columns={[
          { key: 'code', header: 'Código' },
          { key: 'description', header: 'Descripción' },
          {
            key: 'createdAt',
            header: 'Creado',
            render: (value) => new Date(value).toLocaleDateString('es-ES')
          }
        ]}
        loading={natoLoading}
      />

      {/* Modal Crear */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
        }}
        title="Crear Nuevo Rango"
        size="lg"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nombre"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="ej. Teniente"
              required
            />
            <Input
              label="Abreviación"
              value={formData.abbreviation}
              onChange={(e) => handleInputChange('abbreviation', e.target.value)}
              placeholder="ej. Tte."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nivel"
              type="number"
              value={formData.level}
              onChange={(e) => handleInputChange('level', e.target.value)}
              placeholder="ej. 5"
              required
            />
            <Select
              label="Grado NATO (Opcional)"
              value={formData.NATORank}
              onChange={(e) => handleInputChange('NATORank', e.target.value)}
              options={[
                { value: '', label: 'Sin asignar' },
                ...natoRanks.map(nato => ({
                  value: nato._id,
                  label: `${nato.code} - ${nato.description}`
                }))
              ]}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                setShowCreateModal(false);
                resetForm();
              }}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleCreate}
              loading={creating}
              disabled={!formData.name || !formData.level}
            >
              Crear Rango
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal Editar */}
      <Modal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setEditingRank(null);
          resetForm();
        }}
        title="Editar Rango"
        size="lg"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nombre"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="ej. Teniente"
              required
            />
            <Input
              label="Abreviación"
              value={formData.abbreviation}
              onChange={(e) => handleInputChange('abbreviation', e.target.value)}
              placeholder="ej. Tte."
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nivel"
              type="number"
              value={formData.level}
              onChange={(e) => handleInputChange('level', e.target.value)}
              placeholder="ej. 5"
              required
            />
            <Select
              label="Grado NATO (Opcional)"
              value={formData.NATORank}
              onChange={(e) => handleInputChange('NATORank', e.target.value)}
              options={[
                { value: '', label: 'Sin asignar' },
                ...natoRanks.map(nato => ({
                  value: nato._id,
                  label: `${nato.code} - ${nato.description}`
                }))
              ]}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                setShowEditModal(false);
                setEditingRank(null);
                resetForm();
              }}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleUpdate}
              loading={updating}
              disabled={!formData.name || !formData.level}
            >
              Actualizar Rango
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default RanksTestingPage;
