const express = require('express');
const {
  getAllRanks,
  getRankById,
  getRanksByLevel,
  getRanksByNATORank,
  createRank,
  updateRank,
  deleteRank,
  getRankStats
} = require('../controllers/rankController');

const {
  validateRankCreate,
  validateRankUpdate,
  validateObjectId,
  validateRankFilters
} = require('../middleware/validation');

/**
 * Ranks Routes
 * Sprint Backend: API de Rangos
 * 
 * Routes for rank CRUD operations with NATO rank relationships
 * Base path: /api/v1/ranks
 */

const router = express.Router();

// GET /api/v1/ranks/stats - Get statistics (must be before /:id)
router.get('/stats', getRankStats);

// GET /api/v1/ranks/level/:level - Get ranks by level (must be before /:id)
router.get('/level/:level', getRanksByLevel);

// GET /api/v1/ranks/nato/:natoRankId - Get ranks by NATO rank (must be before /:id)
router.get('/nato/:natoRankId', validateObjectId, getRanksByNATORank);

// GET /api/v1/ranks - Get all ranks with pagination and filters
router.get('/', validateRankFilters, getAllRanks);

// GET /api/v1/ranks/:id - Get single rank by ID
router.get('/:id', validateObjectId, getRankById);

// POST /api/v1/ranks - Create new rank
router.post('/', validateRankCreate, createRank);

// PUT /api/v1/ranks/:id - Update rank
router.put('/:id', validateObjectId, validateRankUpdate, updateRank);

// DELETE /api/v1/ranks/:id - Delete rank
router.delete('/:id', validateObjectId, deleteRank);

module.exports = router;
