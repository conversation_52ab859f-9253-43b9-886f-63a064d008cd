{"name": "personnel-management-system", "version": "1.0.0", "description": "Sistema de Gestión de Personal Militar - Monorepo", "private": true, "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "seed": "cd backend && npm run seed", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^8.2.0"}, "workspaces": ["backend", "frontend"], "keywords": ["personnel", "management", "military", "monorepo"], "author": "KPLdeV", "license": "MIT"}