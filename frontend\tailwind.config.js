/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Excel-like theme colors from project specification
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb', // primary-color: blue-600
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        accent: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e', // accent-color: green-500
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444', // error-color: red-500
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // Excel-like grays
        excel: {
          header: '#f3f4f6', // header_background: bg-gray-200
          border: '#d1d5db', // cell_borders: border-gray-300
          row1: '#ffffff',   // row_alternate_colors: bg-white
          row2: '#f9fafb',   // row_alternate_colors: bg-gray-50
        },
        // Shift duration colors
        shift: {
          'day-1': '#dbeafe', // 1_day: bg-blue-100
          'day-2': '#dcfce7', // 2_days: bg-green-100
          'day-3': '#fef3c7', // 3_days: bg-yellow-100
        },
        // Request status colors
        status: {
          pending: '#fef3c7',   // pending: bg-yellow-200
          approved: '#bbf7d0',  // approved: bg-green-200
          rejected: '#fecaca',  // rejected: bg-red-200
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['ui-monospace', 'SFMono-Regular', 'Monaco', 'Consolas', 'Liberation Mono', 'Menlo', 'monospace'],
      },
      borderColor: {
        DEFAULT: '#d1d5db', // Default border color for Excel-like cells
      },
      boxShadow: {
        'excel': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'excel-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      },
      animation: {
        'drag-highlight': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      }
    },
  },
  plugins: [],
}

