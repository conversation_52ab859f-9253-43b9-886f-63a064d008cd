@tailwind base;
@tailwind components;
@tailwind utilities;

/* Excel-like Base Styles */
@layer base {
  :root {
    /* Excel-like color scheme */
    --color-excel-header: #f3f4f6;
    --color-excel-border: #d1d5db;
    --color-excel-row1: #ffffff;
    --color-excel-row2: #f9fafb;
    --color-primary: #2563eb;
    --color-accent: #22c55e;
    --color-error: #ef4444;
  }

  * {
    @apply border-excel-border;
  }

  html {
    @apply font-sans text-sm;
  }

  body {
    @apply bg-white text-gray-900 antialiased;
    margin: 0;
    min-height: 100vh;
  }

  /* Excel-like focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-1;
  }
}

/* Excel-like Component Styles */
@layer components {
  /* Excel-like table styles */
  .excel-table {
    @apply w-full border-collapse;
  }

  .excel-cell {
    @apply border border-excel-border px-2 py-1 text-left;
    min-height: 24px;
    font-size: 11px;
    line-height: 1.2;
  }

  .excel-header {
    @apply bg-excel-header font-semibold text-gray-700 border-excel-border;
  }

  .excel-row-even {
    @apply bg-excel-row1;
  }

  .excel-row-odd {
    @apply bg-excel-row2;
  }

  .excel-row:hover {
    @apply bg-blue-50;
  }

  /* Excel-like input styles */
  .excel-input {
    @apply w-full h-full border-none bg-transparent px-1 py-0 text-xs;
    outline: none;
  }

  .excel-input:focus {
    @apply ring-0 outline-none;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-error {
    @apply bg-error-500 hover:bg-error-600 text-white px-4 py-2 rounded-md font-medium transition-colors;
  }

  /* Form styles */
  .form-input {
    @apply border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-primary-500 focus:border-primary-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-error-500 text-xs mt-1;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-excel border border-gray-200 p-6;
  }

  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }

  /* Tooltip styles */
  .tooltip {
    @apply bg-gray-800 text-white p-2 rounded shadow-lg text-xs;
  }

  /* Drag highlight styles */
  .drag-highlight {
    @apply border-dashed border-2 border-blue-400 animate-drag-highlight;
  }

  /* Status indicators */
  .status-pending {
    @apply bg-status-pending text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-approved {
    @apply bg-status-approved text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-rejected {
    @apply bg-status-rejected text-red-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Shift duration indicators */
  .shift-1-day {
    @apply bg-shift-day-1 text-blue-800;
  }

  .shift-2-days {
    @apply bg-shift-day-2 text-green-800;
  }

  .shift-3-days {
    @apply bg-shift-day-3 text-yellow-800;
  }
}

/* Utility classes */
@layer utilities {
  .text-excel {
    font-size: 11px;
    line-height: 1.2;
  }

  .h-excel-row {
    height: 24px;
  }

  .min-h-excel-row {
    min-height: 24px;
  }
}
