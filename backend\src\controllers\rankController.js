const Rank = require('../models/Rank');
const NATORank = require('../models/NATORank');

/**
 * Rank Controller
 * Sprint Backend: API de Rangos
 * 
 * CRUD operations for Rank entities with NATO rank population
 * Endpoints: GET, POST, PUT, DELETE /api/v1/ranks
 */

/**
 * @desc    Get all ranks with pagination and filters
 * @route   GET /api/v1/ranks
 * @access  Public (will be protected later)
 */
const getAllRanks = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, level, natoRank } = req.query;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    if (level) filter.level = level;
    if (natoRank) filter.NATORank = natoRank;

    // Get total count for pagination
    const total = await Rank.countDocuments(filter);
    
    // Get paginated results with NATO rank population
    const ranks = await Rank.find(filter)
      .populate('NATORank', 'code description')
      .sort({ level: 1 }) // Sort by level ascending
      .skip(skip)
      .limit(parseInt(limit));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      success: true,
      data: ranks,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      },
      filters: {
        level: level || null,
        natoRank: natoRank || null
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get single rank by ID
 * @route   GET /api/v1/ranks/:id
 * @access  Public (will be protected later)
 */
const getRankById = async (req, res, next) => {
  try {
    const rank = await Rank.findById(req.params.id)
      .populate('NATORank', 'code description');

    if (!rank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Rank not found'
      });
    }

    res.status(200).json({
      success: true,
      data: rank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get ranks by level
 * @route   GET /api/v1/ranks/level/:level
 * @access  Public (will be protected later)
 */
const getRanksByLevel = async (req, res, next) => {
  try {
    const level = parseInt(req.params.level);
    
    if (isNaN(level) || level < 1 || level > 50) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Level',
        message: 'Level must be a number between 1 and 50'
      });
    }

    const ranks = await Rank.findByLevel(level);

    res.status(200).json({
      success: true,
      data: ranks,
      count: ranks.length
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get ranks by NATO rank
 * @route   GET /api/v1/ranks/nato/:natoRankId
 * @access  Public (will be protected later)
 */
const getRanksByNATORank = async (req, res, next) => {
  try {
    const ranks = await Rank.findByNATORank(req.params.natoRankId);

    res.status(200).json({
      success: true,
      data: ranks,
      count: ranks.length
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Create new rank
 * @route   POST /api/v1/ranks
 * @access  Private (will be protected later)
 */
const createRank = async (req, res, next) => {
  try {
    // Validate NATO rank exists if provided
    if (req.body.NATORank) {
      const natoRankExists = await NATORank.findById(req.body.NATORank);
      if (!natoRankExists) {
        return res.status(400).json({
          success: false,
          error: 'Invalid Reference',
          message: 'Referenced NATO rank does not exist'
        });
      }
    }

    const rank = await Rank.create(req.body);
    
    // Populate NATO rank for response
    await rank.populate('NATORank', 'code description');

    res.status(201).json({
      success: true,
      message: 'Rank created successfully',
      data: rank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update rank
 * @route   PUT /api/v1/ranks/:id
 * @access  Private (will be protected later)
 */
const updateRank = async (req, res, next) => {
  try {
    // Validate NATO rank exists if provided
    if (req.body.NATORank) {
      const natoRankExists = await NATORank.findById(req.body.NATORank);
      if (!natoRankExists) {
        return res.status(400).json({
          success: false,
          error: 'Invalid Reference',
          message: 'Referenced NATO rank does not exist'
        });
      }
    }

    const rank = await Rank.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true, // Return updated document
        runValidators: true // Run schema validations
      }
    ).populate('NATORank', 'code description');

    if (!rank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Rank not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Rank updated successfully',
      data: rank
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Delete rank
 * @route   DELETE /api/v1/ranks/:id
 * @access  Private (will be protected later)
 */
const deleteRank = async (req, res, next) => {
  try {
    const rank = await Rank.findById(req.params.id);

    if (!rank) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Rank not found'
      });
    }

    // TODO: Check if rank is being used by any personnel when Person model is available
    // const Personnel = require('../models/Person');
    // const personnelWithThisRank = await Personnel.countDocuments({ rank: req.params.id });

    await Rank.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Rank deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get rank statistics
 * @route   GET /api/v1/ranks/stats
 * @access  Public (will be protected later)
 */
const getRankStats = async (req, res, next) => {
  try {
    const total = await Rank.countDocuments();
    const withNATORank = await Rank.countDocuments({ NATORank: { $exists: true, $ne: null } });
    const withoutNATORank = total - withNATORank;

    // Get level distribution
    const levelDistribution = await Rank.aggregate([
      {
        $group: {
          _id: '$level',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        total,
        withNATORank,
        withoutNATORank,
        levelDistribution
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllRanks,
  getRankById,
  getRanksByLevel,
  getRanksByNATORank,
  createRank,
  updateRank,
  deleteRank,
  getRankStats
};
