/**
 * Jest Test Setup
 * Sprint BD: Entidades de Rangos
 *
 * Global test configuration and setup
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = 'mongodb://localhost:27017/personnel_management_test';

// Increase timeout for database operations
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  // Add any global test utilities here
};