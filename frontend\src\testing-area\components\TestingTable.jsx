import { useState } from 'react';
import { Button, Loading } from '../../components/common';
import clsx from 'clsx';

/**
 * TestingTable Component - Tabla para mostrar datos de prueba
 * NOTA: Este componente será eliminado al finalizar el desarrollo
 * 
 * Tabla Excel-like para mostrar y probar datos de la iteración actual
 */
const TestingTable = ({
  data = [],
  columns = [],
  loading = false,
  error = null,
  title = 'Datos de Prueba',
  onRefresh,
  onEdit,
  onDelete,
  onAdd
}) => {
  const [selectedRows, setSelectedRows] = useState(new Set());

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedRows(new Set(data.map(item => item._id || item.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleSelectRow = (id, checked) => {
    const newSelected = new Set(selectedRows);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedRows(newSelected);
  };

  if (loading) {
    return (
      <div className="card">
        <Loading text="Cargando datos de prueba..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="text-center py-8">
          <div className="text-error-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar datos</h3>
          <p className="text-gray-600 mb-4">{error.message || 'Error desconocido'}</p>
          {onRefresh && (
            <Button onClick={onRefresh} variant="primary">
              Reintentar
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      {/* Header */}
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-sm text-gray-600">
              {data.length} registro{data.length !== 1 ? 's' : ''} encontrado{data.length !== 1 ? 's' : ''}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {onRefresh && (
              <Button onClick={onRefresh} variant="secondary" size="sm">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Actualizar
              </Button>
            )}
            {onAdd && (
              <Button onClick={onAdd} variant="primary" size="sm">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Agregar
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      {data.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay datos</h3>
          <p className="text-gray-600">No se encontraron registros para mostrar</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="excel-table">
            <thead>
              <tr className="excel-header">
                <th className="excel-cell w-12">
                  <div className="flex items-center justify-center h-full">
                    <input
                      type="checkbox"
                      checked={selectedRows.size === data.length && data.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </div>
                </th>
                {columns.map((column, index) => (
                  <th key={index} className="excel-cell">
                    {column.header}
                  </th>
                ))}
                <th className="excel-cell w-24">Acciones</th>
              </tr>
            </thead>
            <tbody>
              {Array.isArray(data) ? data.map((item, rowIndex) => {
                const itemId = item._id || item.id;
                const isSelected = selectedRows.has(itemId);
                
                return (
                  <tr
                    key={itemId}
                    className={clsx(
                      'excel-row h-8',
                      rowIndex % 2 === 0 ? 'excel-row-even' : 'excel-row-odd',
                      isSelected && 'bg-primary-50'
                    )}
                  >
                    <td className="excel-cell">
                      <div className="flex items-center justify-center h-full">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => handleSelectRow(itemId, e.target.checked)}
                          className="w-4 h-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                      </div>
                    </td>
                    {columns.map((column, colIndex) => (
                      <td key={colIndex} className="excel-cell">
                        {column.render ? column.render(item[column.key], item) : item[column.key]}
                      </td>
                    ))}
                    <td className="excel-cell">
                      <div className="flex items-center justify-center space-x-1 h-full">
                        {onEdit && (
                          <Button
                            onClick={() => onEdit(item)}
                            variant="ghost"
                            size="sm"
                            className="p-0.5 h-6 w-6 text-blue-600 hover:text-blue-700 hover:bg-blue-50 flex items-center justify-center"
                            title="Editar"
                          >
                            ✏️
                          </Button>
                        )}
                        {onDelete && (
                          <Button
                            onClick={() => onDelete(item)}
                            variant="ghost"
                            size="sm"
                            className="p-0.5 h-6 w-6 text-red-600 hover:text-red-700 hover:bg-red-50 flex items-center justify-center"
                            title="Eliminar"
                          >
                            🗑️
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              }) : (
                <tr>
                  <td colSpan={columns.length + 1} className="excel-cell text-center text-gray-500 py-8">
                    {loading ? 'Cargando datos...' : 'No hay datos disponibles'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Selected info */}
      {selectedRows.size > 0 && (
        <div className="border-t border-gray-200 px-6 py-3 bg-gray-50">
          <p className="text-sm text-gray-700">
            {selectedRows.size} elemento{selectedRows.size !== 1 ? 's' : ''} seleccionado{selectedRows.size !== 1 ? 's' : ''}
          </p>
        </div>
      )}
    </div>
  );
};

export default TestingTable;
