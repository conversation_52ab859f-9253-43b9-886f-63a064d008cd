const mongoose = require('mongoose');
const NATORank = require('../src/models/NATORank');
const Rank = require('../src/models/Rank');

/**
 * Test Suite for Sprint BD: Entidades de Rangos
 *
 * Tests for:
 * - NATORank model validation and methods
 * - Rank model validation and methods
 * - Model relationships and population
 * - Data integrity constraints
 */

// Test database connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/personnel_management_test';

beforeAll(async () => {
  await mongoose.connect(MONGODB_URI);
});

afterAll(async () => {
  await mongoose.connection.close();
});

beforeEach(async () => {
  // Clean database before each test
  await NATORank.deleteMany({});
  await Rank.deleteMany({});
});

describe('NATORank Model', () => {
  describe('Validation', () => {
    test('should create a valid NATORank', async () => {
      const natoRank = new NATORank({
        code: 'OR-1',
        description: 'Private/Soldier'
      });

      const savedNATORank = await natoRank.save();
      expect(savedNATORank._id).toBeDefined();
      expect(savedNATORank.code).toBe('OR-1');
      expect(savedNATORank.description).toBe('Private/Soldier');
      expect(savedNATORank.createdAt).toBeDefined();
      expect(savedNATORank.updatedAt).toBeDefined();
    });

    test('should require code field', async () => {
      const natoRank = new NATORank({
        description: 'Test description'
      });

      await expect(natoRank.save()).rejects.toThrow('NATO rank code is required');
    });

    test('should enforce unique code constraint', async () => {
      const natoRank1 = new NATORank({ code: 'OR-1', description: 'First' });
      const natoRank2 = new NATORank({ code: 'OR-1', description: 'Second' });

      await natoRank1.save();
      await expect(natoRank2.save()).rejects.toThrow();
    });

    test('should convert code to uppercase', async () => {
      const natoRank = new NATORank({
        code: 'or-1',
        description: 'Test'
      });

      const savedNATORank = await natoRank.save();
      expect(savedNATORank.code).toBe('OR-1');
    });

    test('should validate code format', async () => {
      const natoRank = new NATORank({
        code: 'invalid code!',
        description: 'Test'
      });

      await expect(natoRank.save()).rejects.toThrow('NATO rank code must contain only uppercase letters, numbers, and hyphens');
    });
  });

  describe('Static Methods', () => {
    test('findByCode should work correctly', async () => {
      const natoRank = new NATORank({
        code: 'OR-2',
        description: 'Test'
      });
      await natoRank.save();

      const found = await NATORank.findByCode('or-2'); // Test case insensitive
      expect(found).toBeTruthy();
      expect(found.code).toBe('OR-2');
    });

    test('findByCode should return null for non-existent code', async () => {
      const found = await NATORank.findByCode('NON-EXISTENT');
      expect(found).toBeNull();
    });
  });
});

describe('Rank Model', () => {
  let natoRank;

  beforeEach(async () => {
    // Create a NATO rank for testing relationships
    natoRank = new NATORank({
      code: 'OR-1',
      description: 'Private/Soldier'
    });
    await natoRank.save();
  });

  describe('Validation', () => {
    test('should create a valid Rank', async () => {
      const rank = new Rank({
        name: 'Soldado',
        level: 1,
        abbreviation: 'SLD',
        NATORank: natoRank._id
      });

      const savedRank = await rank.save();
      expect(savedRank._id).toBeDefined();
      expect(savedRank.name).toBe('Soldado');
      expect(savedRank.level).toBe(1);
      expect(savedRank.abbreviation).toBe('SLD');
      expect(savedRank.NATORank.toString()).toBe(natoRank._id.toString());
      expect(savedRank.createdAt).toBeDefined();
      expect(savedRank.updatedAt).toBeDefined();
    });

    test('should require name field', async () => {
      const rank = new Rank({
        level: 1
      });

      await expect(rank.save()).rejects.toThrow('Rank name is required');
    });

    test('should require level field', async () => {
      const rank = new Rank({
        name: 'Test Rank'
      });

      await expect(rank.save()).rejects.toThrow('Rank level is required');
    });

    test('should enforce unique name constraint', async () => {
      const rank1 = new Rank({ name: 'Soldado', level: 1 });
      const rank2 = new Rank({ name: 'Soldado', level: 2 });

      await rank1.save();
      await expect(rank2.save()).rejects.toThrow();
    });

    test('should validate level range', async () => {
      const rankLow = new Rank({ name: 'Test Low', level: 0 });
      const rankHigh = new Rank({ name: 'Test High', level: 51 });

      await expect(rankLow.save()).rejects.toThrow('Rank level must be at least 1');
      await expect(rankHigh.save()).rejects.toThrow('Rank level cannot exceed 50');
    });

    test('should convert abbreviation to uppercase', async () => {
      const rank = new Rank({
        name: 'Test Rank',
        level: 1,
        abbreviation: 'test'
      });

      const savedRank = await rank.save();
      expect(savedRank.abbreviation).toBe('TEST');
    });
  });

  describe('Static Methods', () => {
    test('findByLevel should work correctly', async () => {
      const rank1 = new Rank({ name: 'Rank 1', level: 5, NATORank: natoRank._id });
      const rank2 = new Rank({ name: 'Rank 2', level: 5, NATORank: natoRank._id });
      const rank3 = new Rank({ name: 'Rank 3', level: 6, NATORank: natoRank._id });

      await rank1.save();
      await rank2.save();
      await rank3.save();

      const found = await Rank.findByLevel(5);
      expect(found).toHaveLength(2);
      expect(found[0].NATORank).toBeDefined(); // Should be populated
    });

    test('findByNATORank should work correctly', async () => {
      const rank = new Rank({ name: 'Test Rank', level: 1, NATORank: natoRank._id });
      await rank.save();

      const found = await Rank.findByNATORank(natoRank._id);
      expect(found).toHaveLength(1);
      expect(found[0].name).toBe('Test Rank');
      expect(found[0].NATORank).toBeDefined(); // Should be populated
    });
  });

  describe('Instance Methods', () => {
    test('getNATOCode should return NATO code when NATORank is set', async () => {
      const rank = new Rank({
        name: 'Test Rank',
        level: 1,
        NATORank: natoRank._id
      });
      await rank.save();

      const natoCode = await rank.getNATOCode();
      expect(natoCode).toBe('OR-1');
    });

    test('getNATOCode should return null when NATORank is not set', async () => {
      const rank = new Rank({
        name: 'Test Rank',
        level: 1
      });
      await rank.save();

      const natoCode = await rank.getNATOCode();
      expect(natoCode).toBeNull();
    });

    test('getEligiblePersons should return empty array (placeholder)', async () => {
      const rank = new Rank({
        name: 'Test Rank',
        level: 1
      });
      await rank.save();

      const persons = await rank.getEligiblePersons();
      expect(Array.isArray(persons)).toBe(true);
      expect(persons).toHaveLength(0);
    });
  });
});