{"version": 3, "file": "flow-producer.js", "sourceRoot": "", "sources": ["../../../src/classes/flow-producer.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAEtC,+BAA0B;AAY1B,oCAAgE;AAChE,+BAA4B;AAC5B,6CAAkD;AAClD,yDAAqD;AACrD,oCAAyD;AA8DzD;;;;;;;GAOG;AACH,MAAa,YAAa,SAAQ,qBAAY;IAY5C,YACS,OAAyB,EAAE,UAAU,EAAE,EAAE,EAAE,EAClD,aAAqC,kCAAe;QAEpD,KAAK,EAAE,CAAC;QAHD,SAAI,GAAJ,IAAI,CAAuC;QAKlD,IAAI,CAAC,IAAI,mBACP,MAAM,EAAE,MAAM,IACX,IAAI,CACR,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE;YAChD,MAAM,EAAE,IAAA,uBAAe,EAAC,IAAI,CAAC,UAAU,CAAC;YACxC,QAAQ,EAAE,KAAK;YACf,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aAC5B;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,sBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE5C,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SACjC;IACH,CAAC;IAED,IAAI,CACF,KAAQ,EACR,GAAG,IAAyC;QAE5C,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,GAAG,CACD,SAAY,EACZ,QAAiC;QAEjC,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,EAAE,CACA,KAAQ,EACR,QAAiC;QAEjC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CACF,KAAQ,EACR,QAAiC;QAEjC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAc,GAAG;QACf,OAAO,SAAG,CAAC;IACb,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,GAAG,CAAC,IAAa,EAAE,IAAe;;QACtC,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAE7B,MAAM,UAAU,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,MAAM,CAAC;QACtC,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,CAAC,CAAC;QAC3C,MAAM,qBAAqB,GAAG,SAAS;YACrC,CAAC,CAAC,GAAG,SAAS,eAAe;YAC7B,CAAC,CAAC,SAAS,CAAC;QAEd,OAAO,IAAA,aAAK,EACV,IAAI,CAAC,SAAS,EACd,gBAAQ,CAAC,QAAQ,EACjB,IAAI,CAAC,SAAS,EACd,SAAS,EACT,IAAI,CAAC,SAAS,EACd,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;gBAClB,CAAC,2BAAmB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI;aAC1C,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAClC,KAAK;gBACL,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa;gBAC/B,MAAM,EAAE;oBACN,UAAU;oBACV,qBAAqB;iBACtB;aACF,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;YAEnB,OAAO,QAAQ,CAAC;QAClB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CAAC,IAAc;QAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAE5C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAC/B;YACE,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;SACzB,EACD,IAAI,CACL,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAEnD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,OAAO,CAAC,KAAgB;QAC5B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAE7B,OAAO,IAAA,aAAK,EACV,IAAI,CAAC,SAAS,EACd,gBAAQ,CAAC,QAAQ,EACjB,EAAE,EACF,cAAc,EACd,EAAE,EACF,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;gBAClB,CAAC,2BAAmB,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM;gBAC7C,CAAC,2BAAmB,CAAC,SAAS,CAAC,EAAE,KAAK;qBACnC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;qBACtB,IAAI,CAAC,GAAG,CAAC;aACb,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAEpD,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;YAEnB,OAAO,SAAS,CAAC;QACnB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACO,KAAK,CAAC,OAAO,CAAC,EACtB,KAAK,EACL,IAAI,EACJ,MAAM,EACN,UAAU,GACE;;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,sBAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;QACtE,MAAM,SAAS,GAAG,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE3D,MAAM,QAAQ,GAAG,MAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,iBAAiB,mCAAI,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,KAAK,KAAI,IAAA,SAAE,GAAE,CAAC;QAEvC,OAAO,IAAA,aAAK,EACV,IAAI,CAAC,SAAS,EACd,gBAAQ,CAAC,QAAQ,EACjB,IAAI,CAAC,SAAS,EACd,SAAS,EACT,IAAI,CAAC,SAAS,EACd,KAAK,EAAE,IAAI,EAAE,sBAAsB,EAAE,EAAE;;YACrC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;gBAClB,CAAC,2BAAmB,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI;gBACxC,CAAC,2BAAmB,CAAC,KAAK,CAAC,EAAE,KAAK;aACnC,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,SAAS,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,CAAC;YAEhC,IAAI,sBAAsB,IAAI,IAAI,EAAE;gBAClC,MAAM,WAAW,GAAG,MAAA,IAAI,CAAC,SAAS,0CAAE,WAAW,CAAC;gBAChD,MAAM,iBAAiB,GACrB,CAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ;oBACxB,CAAC,CAAC,WAAW,IAAI,sBAAsB,CAAC,CAAC;gBAE3C,IAAI,iBAAiB,IAAI,WAAW,EAAE;oBACpC,SAAS,GAAG;wBACV,QAAQ,EAAE,iBAAiB;wBAC3B,WAAW;qBACZ,CAAC;iBACH;aACF;YAED,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CACtB,KAAK,EACL,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,gDAEJ,QAAQ,GACR,IAAI,KACP,MAAM,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,EAC1B,SAAS,KAEX,KAAK,CACN,CAAC;YAEF,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7C,wEAAwE;gBACxE,MAAM,QAAQ,GAAG,KAAK,CAAC;gBACvB,MAAM,eAAe,GAAG,IAAI,sBAAS,CACnC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAChC,CAAC;gBACF,MAAM,eAAe,GAAG,eAAe,CAAC,KAAK,CAC3C,IAAI,CAAC,SAAS,EACd,kBAAkB,CACnB,CAAC;gBAEF,MAAM,GAAG,CAAC,MAAM,CAAS,KAAiB,EAAE;oBAC1C,qBAAqB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,qBAAqB;oBACpD,eAAe;oBACf,SAAS;iBACV,CAAC,CAAC;gBAEH,MAAM,qBAAqB,GAAG,GAAG,eAAe,CAAC,KAAK,CACpD,IAAI,CAAC,SAAS,EACd,QAAQ,CACT,eAAe,CAAC;gBAEjB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC;oBACtC,KAAK;oBACL,KAAK,EAAE,IAAI,CAAC,QAAQ;oBACpB,MAAM,EAAE;wBACN,UAAU,EAAE;4BACV,EAAE,EAAE,QAAQ;4BACZ,KAAK,EAAE,eAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC;yBAC7D;wBACD,qBAAqB;qBACtB;oBACD,UAAU;iBACX,CAAC,CAAC;gBAEH,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;aAC1B;iBAAM;gBACL,MAAM,GAAG,CAAC,MAAM,CAAS,KAAiB,EAAE;oBAC1C,qBAAqB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,qBAAqB;oBACpD,SAAS;iBACV,CAAC,CAAC;gBAEH,OAAO,EAAE,GAAG,EAAE,CAAC;aAChB;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACO,QAAQ,CAChB,KAAyB,EACzB,KAAgB;QAEhB,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;YACf,MAAM,UAAU,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,MAAM,CAAC;YACtC,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,qBAAqB,GAAG,SAAS;gBACrC,CAAC,CAAC,GAAG,SAAS,eAAe;gBAC7B,CAAC,CAAC,SAAS,CAAC;YAEd,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,KAAK;gBACL,IAAI;gBACJ,MAAM,EAAE;oBACN,UAAU;oBACV,qBAAqB;iBACtB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,MAAmB,EAAE,IAAc;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAC9B,IAAI,EACJ,IAAI,sBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAC1B,IAAI,CAAC,MAAM,CACZ,CAAC;QAEF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAElD,IAAI,GAAG,EAAE;YACP,MAAM,EACJ,SAAS,GAAG,EAAE,EACd,WAAW,GAAG,EAAE,EAChB,MAAM,GAAG,EAAE,EACX,OAAO,GAAG,EAAE,GACb,GAAG,MAAM,GAAG,CAAC,eAAe,CAAC;gBAC5B,MAAM,EAAE;oBACN,KAAK,EAAE,IAAI,CAAC,WAAW;iBACxB;gBACD,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI,CAAC,WAAW;iBACxB;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,WAAW;iBACxB;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI,CAAC,WAAW;iBACxB;aACF,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEzC,MAAM,aAAa,GACjB,aAAa,CAAC,MAAM;gBACpB,WAAW,CAAC,MAAM;gBAClB,WAAW,CAAC,MAAM;gBAClB,MAAM,CAAC,MAAM,CAAC;YAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YAChC,IAAI,aAAa,GAAG,CAAC,IAAI,QAAQ,EAAE;gBACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CACrC,MAAM,EACN,CAAC,GAAG,aAAa,EAAE,GAAG,WAAW,EAAE,GAAG,MAAM,EAAE,GAAG,WAAW,CAAC,EAC7D,QAAQ,EACR,IAAI,CAAC,WAAW,CACjB,CAAC;gBAEF,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;aAC1B;iBAAM;gBACL,OAAO,EAAE,GAAG,EAAE,CAAC;aAChB;SACF;IACH,CAAC;IAEO,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAmB;QACvE,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CACrE,CAAC;IACJ,CAAC;IAEO,WAAW,CACjB,MAAmB,EACnB,YAAsB,EACtB,KAAa,EACb,WAAmB;QAEnB,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE;YAC/B,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE/C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBAC1B,EAAE;gBACF,SAAS;gBACT,MAAM;gBACN,KAAK;gBACL,WAAW;aACZ,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;OAOG;IACK,aAAa,CACnB,IAAoD,EACpD,SAAoB,EACpB,MAAc;QAEd,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YAC9B,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;YAC9D,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;YAChC,aAAa,EAAE,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC;YAC9D,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,cAAc,EAAE,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;YAClD,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAQ;YACrD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAQ;YACjC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAQ;YAC7B,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;YAC1C,KAAK,EAAE,KAAK,IAAkB,EAAE,GAAE,CAAC;SACpC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;SACxC;QACD,MAAM,IAAI,CAAC,OAAO,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;CACF;AAzeD,oCAyeC"}