const mongoose = require('mongoose');

/**
 * Rank Model
 * Represents military ranks with NATO reference
 *
 * Fields according to project.json:
 * - _id: ObjectId
 * - name: String, required, unique
 * - level: Number, required
 * - abbreviation: String
 * - NATORank: ObjectId, ref: NATORank
 * - createdAt: Date
 * - updatedAt: Date
 *
 * Indexes: ["name", "level", "NATORank"]
 * Methods: ["getEligiblePersons()", "getNATOCode()"]
 */
const RankSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Rank name is required'],
    unique: true,
    trim: true,
    maxlength: [100, 'Rank name cannot exceed 100 characters']
  },
  level: {
    type: Number,
    required: [true, 'Rank level is required'],
    min: [1, 'Rank level must be at least 1'],
    max: [50, 'Rank level cannot exceed 50']
  },
  abbreviation: {
    type: String,
    trim: true,
    uppercase: true,
    maxlength: [10, 'Abbreviation cannot exceed 10 characters']
  },
  NATORank: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NATORank',
    index: true
  }
}, {
  timestamps: true, // Automatically adds createdAt and updatedAt
  collection: 'ranks'
});

// Indexes
RankSchema.index({ name: 1 });
RankSchema.index({ level: 1 });
RankSchema.index({ NATORank: 1 });

// Instance methods as specified in project.json
RankSchema.methods.getEligiblePersons = function() {
  // TODO: Implement when Person model is available
  // This method should return persons eligible for this rank
  return Promise.resolve([]);
};

RankSchema.methods.getNATOCode = async function() {
  if (!this.NATORank) {
    return null;
  }

  await this.populate('NATORank');
  return this.NATORank ? this.NATORank.code : null;
};

// Static methods
RankSchema.statics.findByLevel = function(level) {
  return this.find({ level: level }).populate('NATORank');
};

RankSchema.statics.findByNATORank = function(natoRankId) {
  return this.find({ NATORank: natoRankId }).populate('NATORank');
};

// Pre-save middleware
RankSchema.pre('save', function(next) {
  if (this.abbreviation) {
    this.abbreviation = this.abbreviation.toUpperCase();
  }
  next();
});

// Export model
const Rank = mongoose.model('Rank', RankSchema);

module.exports = Rank;